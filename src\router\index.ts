import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/main-view/index.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      redirect: '/section-monitoring',
      children: [
        {
          path: 'section-monitoring',
          name: 'section-monitoring',
          component: () => import('../views/section-monitoring/index.vue'),
        },
      ],
    },
  ],
})

export default router
