<template>
  <n-input-group class="v-input-group">
    <n-input-group-label :size="size">{{ label }}</n-input-group-label>
    <slot></slot>
  </n-input-group>
</template>

<script setup lang="ts">
import { SearchIcon } from '@/utils/constant/icons'
import { NInputGroup, NInputGroupLabel } from 'naive-ui'
import type { InputOtpSize } from 'naive-ui'

// 定义 props
interface Props {
  label?: string
  size?: InputOtpSize
}

console.log('🚀 ~ SearchIcon:', SearchIcon)

withDefaults(defineProps<Props>(), {
  size: 'large',
})
</script>

<style>
.v-input-group .n-input__border {
  border-left: 0;
}

.v-input-group .n-input .n-input-wrapper {
  padding-left: 6px;
}

.v-input-group .n-input-group-label {
  padding-right: 6px;
}

.v-input-group .n-base-selection__border {
  border-left: 0;
}
</style>
