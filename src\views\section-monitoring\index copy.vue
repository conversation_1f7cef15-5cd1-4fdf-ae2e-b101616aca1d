<template>
  <div>
    <n-data-table
      class="v-data-table"
      ref="table"
      size="large"
      :columns="columns"
      :data="data"
      :bottom-bordered="false"
      :bordered="false"
      :single-line="false"
      single-column
      row-class-name="row-item"
    />
  </div>
</template>

<script setup lang="ts">
import { NDataTable } from 'naive-ui'

const columns = [
  {
    title: 'Name',
    key: 'name',
    defaultSortOrder: 'ascend',
  },
  {
    title: 'Age',
    key: 'age',
    sorter: (row1, row2) => row1.age - row2.age,
  },
  {
    title: 'Address',
    key: 'address',
    defaultFilterOptionValues: ['London', 'New York'],
    filterOptions: [
      {
        label: 'London',
        value: 'London',
      },
      {
        label: 'New York',
        value: 'New York',
      },
    ],
    filter(value, row) {
      return ~row.address.indexOf(value)
    },
  },
]

const data = [
  {
    key: 0,
    name: '<PERSON>',
    age: 32,
    address: 'New York No. 1 Lake Park',
  },
  {
    key: 1,
    name: '<PERSON>',
    age: 42,
    address: 'London No. 1 Lake Park',
  },
  {
    key: 2,
    name: '<PERSON>',
    age: 32,
    address: 'Sidney No. 1 Lake Park',
  },
  {
    key: 3,
    name: '<PERSON> Red',
    age: 32,
    address: 'London No. 2 Lake Park',
  },
]
</script>
<style>
.v-data-table .n-data-table-table {
  margin-top: -10px;
  border-collapse: separate;
  border-spacing: 0px 10px;
  border: none;
}
.row-item {
  font-size: 20px;
  border: none;
}
</style>
