// 动态导入所有 SVG 图标
const iconModules = import.meta.glob('@/assets/images/icons/*.svg', { eager: true })

// 创建图标对象，将文件名转换为组件名
const icons: Record<string, any> = {}

Object.entries(iconModules).forEach(([path, module]) => {
  // 从路径中提取文件名（不包含扩展名）
  const fileName = path.split('/').pop()?.replace('.svg', '') || ''
  // 将文件名转换为组件名格式（首字母大写 + Icon后缀）
  const componentName = fileName + 'Icon'
  icons[componentName] = (module as any).default
})
console.log('🚀 ~ Object.entries ~ icons:', icons)


// 导出整个图标对象供动态使用
export { ...icons }

// 导出所有图标的类型
export type IconName = keyof typeof icons
